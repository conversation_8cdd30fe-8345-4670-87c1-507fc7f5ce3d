import logging
from typing import Optional

from django.utils import timezone as django_timezone
from asgiref.sync import sync_to_async
from fastapi import Depends, HTTPException, Header, status

from app.models import Device, DeviceSession, UserDevice

logger = logging.getLogger(__name__)


class DeviceAuthInfo:
    def __init__(self, device: Device, session: DeviceSession, user_device: Optional[UserDevice] = None):
        self.device = device
        self.session = session
        self.user_device = user_device
        self.user = user_device.user if user_device else None


def get_device_auth_sync(device_id: str, authorization: str) -> DeviceAuthInfo:
    if not authorization.startswith("Bearer "):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authorization header format. Expected 'Bearer <token>'",
            headers={"WWW-Authenticate": "Bearer"},
        )

    access_token = authorization[7:]

    current_time = django_timezone.now()

    try:
        device = Device.objects.get(device_id=device_id, deleted__isnull=True)
    except Device.DoesNotExist:
        logger.warning(f"Device not found: {device_id}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Device not found or not registered",
            headers={"WWW-Authenticate": "Bearer"},
        )

    try:
        session = DeviceSession.objects.get(
            device=device,
            access_token=access_token,
            expires_at__gt=current_time,
            deleted__isnull=True
        )
    except DeviceSession.DoesNotExist:
        logger.warning(f"Invalid or expired session for device: {device_id}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired device session",
            headers={"WWW-Authenticate": "Bearer"},
        )

    device.last_seen_at = current_time
    device.save(update_fields=['last_seen_at'])

    try:
        user_device = UserDevice.objects.select_related('user').get(
            device=device,
            revoked_at__isnull=True,
            deleted__isnull=True
        )
    except UserDevice.DoesNotExist:
        user_device = None
        logger.warning(f"Device {device_id} is not linked to any user")

    logger.info(f"Device {device_id} authenticated successfully")
    return DeviceAuthInfo(device=device, session=session, user_device=user_device)

@sync_to_async
def get_device_auth_async_wrapper(device_id: str, authorization: str) -> DeviceAuthInfo:
    return get_device_auth_sync(device_id, authorization)


async def get_device_auth(
    device_id: str = Header(..., description="Device identifier"),
    authorization: str = Header(..., description="Bearer token with device access token")
) -> DeviceAuthInfo:
    try:
        return await get_device_auth_async_wrapper(device_id, authorization)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Device authentication error for {device_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication service error",
        )


async def get_authenticated_device(
    device_auth: DeviceAuthInfo = Depends(get_device_auth)
) -> Device:
    return device_auth.device


async def get_device_with_user(
    device_auth: DeviceAuthInfo = Depends(get_device_auth)
) -> DeviceAuthInfo:
    if not device_auth.user:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Device is not linked to any user. Please complete device setup.",
        )
    
    return device_auth
