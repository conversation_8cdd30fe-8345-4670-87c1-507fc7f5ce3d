import logging
from datetime import datetime
from decimal import Decimal
from io import Bytes<PERSON>
from typing import List

from asgiref.sync import sync_to_async
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.db import transaction
from fastapi import APIRouter, Depends, File, Form, HTTPException, UploadFile, status

from app.api.dependencies.device_auth import DeviceAuthInfo, get_device_with_user
from app.api.schemas.meal_logs import (
    AISuggestion,
    ErrorResponse,
    FoodEntryResponse,
    MealLogResponse,
    SessionTotals,
)
from app.models import FoodEntry, MealLog, User
from app.models.device import Device
from app.models.food_entry import FoodEntrySource
from app.models.meal_log import MealLogStatus
from app.services.ai_classification import FoodClassificationResult, ai_service
from app.services.meal_session import meal_session_service

logger = logging.getLogger(__name__)

router = APIRouter()


def _convert_uploadfile_to_django_file(upload_file: UploadFile) -> InMemoryUploadedFile:
    upload_file.file.seek(0)
    file_content = upload_file.file.read()
    return InMemoryUploadedFile(
        file=BytesIO(file_content),
        field_name="image",
        name=upload_file.filename or "meal_image.jpg",
        content_type=upload_file.content_type or "image/jpeg",
        size=len(file_content),
        charset=None,
    )


@sync_to_async
def _get_latest_meal_log(device: Device, user: User) -> MealLog | None:
    try:
        return meal_session_service.get_latest_meal_log(device, user)
    except Exception as e:
        logger.error(f"Error getting latest meal log: {e}")
        return None


@sync_to_async
def _create_meal_log_and_entries(
    device: Device,
    user: User,
    timestamp: datetime,
    image_file: UploadFile,
    total_weight_g: Decimal,
    delta_weight_g: Decimal,
    status_value: MealLogStatus,
    ai_suggestions: List[AISuggestion],
    classification_results: List[FoodClassificationResult],
):
    try:
        with transaction.atomic():
            meal_session = meal_session_service.find_or_create_session(
                device=device, user=user, timestamp=timestamp
            )
            django_image_file = _convert_uploadfile_to_django_file(image_file)
            meal_log = MealLog.objects.create(
                meal_session=meal_session,
                timestamp=timestamp,
                image=django_image_file,
                total_weight_g=total_weight_g,
                delta_weight_g=delta_weight_g,
                status=status_value,
                ai_suggestions=[s.model_dump() for s in ai_suggestions],
            )

            food_entries = []
            previous_log = meal_session_service.get_latest_meal_log(device, user)
            prev_entries = FoodEntry.objects.filter(meal_log=previous_log) if previous_log else []
            prev_labels = {entry.label.lower() for entry in prev_entries}

            new_results = [r for r in classification_results if r.label.lower() not in prev_labels]
            num_new = len(new_results)
            if num_new > 0:
                weight_per_food = delta_weight_g / num_new if num_new > 1 else delta_weight_g
                for r in new_results:
                    r.weight_g = weight_per_food
                    if r.confidence >= 0.4:
                        food_entries.append(
                            FoodEntry.objects.create(
                                meal_log=meal_log,
                                label=r.label,
                                weight_g=r.weight_g,
                                estimated_kcal=r.estimated_kcal,
                                protein_g=r.protein_g,
                                carbs_g=r.carbs_g,
                                fat_g=r.fat_g,
                                fibre_g=r.fibre_g,
                                source=FoodEntrySource.AI_AUTO,
                                manual_override=False,
                            )
                        )

            meal_session_service.update_session_totals(meal_session)
            session_totals = meal_session_service.get_session_totals(meal_session)

            return meal_log, food_entries, session_totals, meal_session
    except Exception as e:
        logger.error(f"Error creating meal session and log: {e}", exc_info=True)
        raise


def _determine_meal_log_status(
    classification_results: List[FoodClassificationResult],
) -> tuple[MealLogStatus, List[AISuggestion]]:
    if not classification_results:
        return MealLogStatus.PENDING_LABEL, []

    ai_suggestions = [
        AISuggestion(label=r.label, confidence=r.confidence)
        for r in classification_results
    ]

    if len(classification_results) == 1 and classification_results[0].confidence >= 0.7:
        return MealLogStatus.CONFIRMED, ai_suggestions

    return MealLogStatus.PENDING_LABEL, ai_suggestions


def _build_meal_log_response(
    meal_log, meal_session, food_entries, session_totals, ai_suggestions
) -> MealLogResponse:
    food_entry_responses = [
        FoodEntryResponse(
            id=entry.id,
            label=entry.label,
            weight_g=entry.weight_g,
            estimated_kcal=entry.estimated_kcal,
            protein_g=entry.protein_g,
            carbs_g=entry.carbs_g,
            fat_g=entry.fat_g,
            fibre_g=entry.fibre_g,
            source=entry.source,
            manual_override=entry.manual_override,
        )
        for entry in food_entries
    ]

    return MealLogResponse(
        meal_log_id=str(meal_log.id),
        meal_session_id=str(meal_session.id),
        status=meal_log.status,
        image_url=meal_log.image.url if meal_log.image else None,
        ai_suggestions=ai_suggestions,
        entries=food_entry_responses,
        session_totals=SessionTotals(**session_totals),
    )


@router.post(
    "/",
    response_model=MealLogResponse,
    status_code=status.HTTP_201_CREATED,
    responses={
        400: {"model": ErrorResponse, "description": "Bad request"},
        401: {"model": ErrorResponse, "description": "Unauthorized"},
        403: {"model": ErrorResponse, "description": "Device not linked to user"},
        422: {"model": ErrorResponse, "description": "Validation error"},
        500: {"model": ErrorResponse, "description": "Internal server error"},
    },
)
async def create_meal_log(
    timestamp: datetime = Form(...),
    total_weight_g: Decimal = Form(..., gt=0),
    delta_weight_g: Decimal = Form(...),
    image_file: UploadFile = File(...),
    device_auth: DeviceAuthInfo = Depends(get_device_with_user),
) -> MealLogResponse:
    try:
        if not image_file.content_type or not image_file.content_type.startswith(
            "image/"
        ):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid image file. Only JPEG and PNG formats are supported.",
            )

        if delta_weight_g < 0 and abs(delta_weight_g) > total_weight_g:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid weight values. Delta weight cannot exceed total weight.",
            )

        logger.info(
            f"Processing meal log for device {device_auth.device.device_id}, user {device_auth.user.id}"
        )

        previous_log = await _get_latest_meal_log(
            device_auth.device, device_auth.user
        )
        previous_image_url = (
            previous_log.image.url if previous_log and previous_log.image else None
        )

        classification_results = await ai_service.classify_food(
            image_file=image_file,
            delta_weight_g=delta_weight_g,
            previous_image_url=previous_image_url,
        )

        status_value, ai_suggestions = _determine_meal_log_status(
            classification_results
        )

        meal_log, food_entries, session_totals, meal_session = (
            await _create_meal_log_and_entries(
                device=device_auth.device,
                user=device_auth.user,
                timestamp=timestamp,
                image_file=image_file,
                total_weight_g=total_weight_g,
                delta_weight_g=delta_weight_g,
                status_value=status_value,
                ai_suggestions=ai_suggestions,
                classification_results=classification_results,
            )
        )

        response = _build_meal_log_response(
            meal_log, meal_session, food_entries, session_totals, ai_suggestions
        )

        logger.info(
            f"Successfully created meal log {meal_log.id} with {len(food_entries)} food entries"
        )
        return response

    except HTTPException:
        raise

    except Exception as e:
        logger.error(f"Error creating meal log: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while processing meal log",
        )
