# Generated by Django 5.1.7 on 2025-07-23 04:57

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("app", "0003_device_calibrationlog_historicalcalibrationlog_and_more"),
    ]

    operations = [
        migrations.<PERSON>er<PERSON><PERSON>(
            model_name="historicalmealsession",
            name="device_id",
            field=models.Char<PERSON>ield(
                help_text="ID of the device that recorded this meal session",
                max_length=64,
            ),
        ),
        migrations.AlterField(
            model_name="mealsession",
            name="device_id",
            field=models.<PERSON>r<PERSON><PERSON>(
                help_text="ID of the device that recorded this meal session",
                max_length=64,
            ),
        ),
    ]
