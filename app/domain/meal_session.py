import logging
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Optional
from django.db import transaction
from app.models import Devi<PERSON>, MealSession, MealLog, FoodEntry, User
from app.models.meal_session import MealSessionLabel

logger = logging.getLogger(__name__)

SESSION_TIMEOUT_MINUTES = 10
SESSION_TIMEOUT = timedelta(minutes=SESSION_TIMEOUT_MINUTES)

def find_or_create_session(device: Device, user: Optional[User], timestamp: datetime) -> MealSession:
    if not user:
        raise ValueError("Cannot create meal session without a linked user")
    cutoff_time = timestamp - SESSION_TIMEOUT
    recent_session = MealSession.objects.filter(
        user=user,
        device_id=device.device_id,
        end_time__gte=cutoff_time,
        deleted__isnull=True
    ).order_by('-end_time').first()
    if recent_session:
        recent_session.end_time = timestamp
        recent_session.save(update_fields=['end_time'])
        logger.info(f"Extended existing meal session {recent_session.id} for user {user.id}")
        return recent_session
    else:
        session = MealSession.objects.create(
            user=user,
            device_id=device.device_id,
            start_time=timestamp,
            end_time=timestamp,
            label=_infer_meal_label(timestamp),
        )
        logger.info(f"Created new meal session {session.id} for user {user.id}")
        return session

def _infer_meal_label(timestamp: datetime) -> str:
    hour = timestamp.hour
    if 5 <= hour < 11:
        return MealSessionLabel.BREAKFAST
    elif 11 <= hour < 16:
        return MealSessionLabel.LUNCH
    elif 16 <= hour < 21:
        return MealSessionLabel.DINNER
    else:
        return MealSessionLabel.SNACK

@transaction.atomic
def update_session_totals(session: MealSession) -> None:
    food_entries = FoodEntry.objects.filter(
        meal_log__meal_session=session,
        deleted__isnull=True
    )
    total_kcal = Decimal('0')
    total_protein = Decimal('0')
    total_carbs = Decimal('0')
    total_fat = Decimal('0')
    total_fibre = Decimal('0')
    for entry in food_entries:
        total_kcal += entry.estimated_kcal
        total_protein += entry.protein_g
        total_carbs += entry.carbs_g
        total_fat += entry.fat_g
        total_fibre += entry.fibre_g
    session.total_kcal = total_kcal
    session.protein_g = total_protein
    session.carbs_g = total_carbs
    session.fat_g = total_fat
    session.fibre_g = total_fibre
    session.save(update_fields=[
        'total_kcal', 'protein_g', 'carbs_g', 'fat_g', 'fibre_g'
    ])
    logger.info(f"Updated session {session.id} totals: {total_kcal} kcal")

def get_session_totals(session: MealSession) -> dict:
    return {
        'total_kcal': session.total_kcal,
        'protein_g': session.protein_g,
        'carbs_g': session.carbs_g,
        'fat_g': session.fat_g,
        'fibre_g': session.fibre_g,
    }

def get_latest_meal_log(device: Device, user: User) -> Optional[MealLog]:
    return MealLog.objects.filter(
        meal_session__user=user,
        meal_session__device_id=device.device_id,
        deleted__isnull=True
    ).order_by('-timestamp').first()

def calculate_delta_weight(current_weight: Decimal, device: Device, user: User) -> Decimal:
    latest_log = get_latest_meal_log(device, user)
    if latest_log:
        return current_weight - latest_log.total_weight_g
    else:
        return current_weight
