import logging
import os
from decimal import Decimal
from typing import List, Optional
from django.core.files.uploadedfile import UploadedFile

logger = logging.getLogger(__name__)

class FoodClassificationResult:
    def __init__(self, label: str, confidence: float, weight_g: Decimal, estimated_kcal: Decimal, protein_g: Decimal = Decimal('0'), carbs_g: Decimal = Decimal('0'), fat_g: Decimal = Decimal('0'), fibre_g: Decimal = Decimal('0')):
        self.label = label
        self.confidence = confidence
        self.weight_g = weight_g
        self.estimated_kcal = estimated_kcal
        self.protein_g = protein_g
        self.carbs_g = carbs_g
        self.fat_g = fat_g
        self.fibre_g = fibre_g

def build_openai_prompt(delta_weight_g: Decimal, previous_image_url: Optional[str]) -> str:
    base_prompt = f"""
    Analyze this food image and identify the food items. The weight added is {delta_weight_g}g.
    
    Please respond with a JSON array of food items, each containing:
    - label: food name
    - confidence: confidence score (0.0 to 1.0)
    - kcal_per_100g: estimated kcal per 100g
    - protein_per_100g: estimated protein (g) per 100g
    - carbs_per_100g: estimated carbs (g) per 100g
    - fat_per_100g: estimated fat (g) per 100g
    - fibre_per_100g: estimated fibre (g) per 100g
    
    If confidence is below 0.4, return an empty array.
    If multiple items have similar confidence (within 0.1), include all of them.
    
    Example response:
    [
        {{
            "label": "Idli",
            "confidence": 0.85,
            "kcal_per_100g": 120,
            "protein_per_100g": 2.5,
            "carbs_per_100g": 23,
            "fat_per_100g": 0.5,
            "fibre_per_100g": 1.2
        }}
    ]
    """
    if previous_image_url:
        base_prompt += f"\n\nThis is a differential analysis. Compare with the previous state to identify only the newly added food items."
    return base_prompt

def parse_openai_response(response, delta_weight_g: Decimal) -> List[FoodClassificationResult]:
    try:
        import json
        import re
        content = response.choices[0].message.content
        match = re.search(r"```json\s*(\[.*?\])\s*```", content, re.DOTALL)
        if match:
            json_str = match.group(1)
        else:
            start_idx = content.find('[')
            end_idx = content.rfind(']') + 1
            if start_idx == -1 or end_idx == 0:
                return []
            json_str = content[start_idx:end_idx]
        predictions = json.loads(json_str)
        weight_factor = delta_weight_g / Decimal('100')
        results = []
        for pred in predictions:
            if pred.get('confidence', 0) >= 0.4:
                def get_val(key):
                    return Decimal(str(pred.get(key, 0)))
                results.append(
                    FoodClassificationResult(
                        label=pred['label'].title(),
                        confidence=pred['confidence'],
                        weight_g=delta_weight_g,
                        estimated_kcal=get_val('kcal_per_100g') * weight_factor,
                        protein_g=get_val('protein_per_100g') * weight_factor,
                        carbs_g=get_val('carbs_per_100g') * weight_factor,
                        fat_g=get_val('fat_per_100g') * weight_factor,
                        fibre_g=get_val('fibre_per_100g') * weight_factor,
                    )
                )
        return results
    except Exception as e:
        logger.error(f"Failed to parse OpenAI response: {e}")
        return []

def classify_food(image_file: UploadedFile, delta_weight_g: Decimal, previous_image_url: Optional[str] = None) -> List[FoodClassificationResult]:
    try:
        import openai
        import base64
        openai_client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        image_bytes = image_file.read()
        image_data = base64.b64encode(image_bytes).decode('utf-8')
        image_file.seek(0)
        prompt = build_openai_prompt(delta_weight_g, previous_image_url)
        response = openai_client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_data}"
                            }
                        }
                    ]
                }
            ],
            max_tokens=500
        )
        return parse_openai_response(response, delta_weight_g)
    except Exception as e:
        logger.error(f"OpenAI classification failed: {e}")
        return []
