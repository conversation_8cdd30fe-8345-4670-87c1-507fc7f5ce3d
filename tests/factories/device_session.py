import factory
from factory.django import DjangoModelFactory
from app.models import DeviceSession
from tests.factories.device import DeviceFactory
from django.utils import timezone
from datetime import timedelta

class DeviceSessionFactory(DjangoModelFactory):
    class Meta:
        model = DeviceSession
    device = factory.SubFactory(DeviceFactory)
    access_token = factory.Faker("md5")
    nonce_used = factory.Faker("md5")
    expires_at = factory.LazyFunction(lambda: timezone.now() + timedelta(days=7))
