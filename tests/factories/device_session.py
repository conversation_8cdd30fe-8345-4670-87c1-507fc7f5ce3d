import factory
from factory.django import DjangoModelFactory
from app.models import DeviceSession
from tests.factories.device import DeviceFactory

class DeviceSessionFactory(DjangoModelFactory):
    class Meta:
        model = DeviceSession
    device = factory.SubFactory(DeviceFactory)
    access_token = factory.Faker("md5")
    nonce_used = factory.Faker("md5")
    expires_at = factory.Faker("future_datetime") 