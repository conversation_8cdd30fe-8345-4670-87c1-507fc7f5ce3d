import factory
from factory.django import DjangoModelFactory
from app.models import UserDevice
from tests.factories.user import UserFactory
from tests.factories.device import DeviceFactory

class UserDeviceFactory(DjangoModelFactory):
    class Meta:
        model = UserDevice
    user = factory.SubFactory(UserFactory)
    device = factory.SubFactory(DeviceFactory)
    is_owner = True
    setup_complete = True 