from tests.factories.device_session import DeviceSessionFactory
from tests.factories.user_device import UserDeviceFactory
from tests.factories.device import DeviceFactory
from tests.factories.user import UserFactory

class DeviceSessionMixin:
    def create_device_session(self, device=None, **kwargs):
        if device is None:
            device = DeviceFactory()
        return DeviceSessionFactory(device=device, **kwargs)
    def create_user_device(self, user=None, device=None, **kwargs):
        if user is None:
            user = UserFactory()
        if device is None:
            device = DeviceFactory()
        return UserDeviceFactory(user=user, device=device, **kwargs)
